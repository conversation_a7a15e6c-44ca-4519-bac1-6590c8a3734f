[{"C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\AppHybrid.js": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\YouTubeWebView.js": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\DeveloperInfo.js": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\SidebarEnhanced.js": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\utils\\adBlocker.js": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\utils\\developerMonetization.js": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\utils\\youtubeApi.js": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\Header.js": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\SettingsEnhanced.js": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\utils\\channelConfig.js": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\DeveloperPanel.js": "12"}, {"size": 251, "mtime": 1754076608615, "results": "13", "hashOfConfig": "14"}, {"size": 7586, "mtime": 1754076531391, "results": "15", "hashOfConfig": "14"}, {"size": 7750, "mtime": 1754077433668, "results": "16", "hashOfConfig": "14"}, {"size": 6535, "mtime": 1754067963000, "results": "17", "hashOfConfig": "14"}, {"size": 10103, "mtime": 1754068100000, "results": "18", "hashOfConfig": "14"}, {"size": 8231, "mtime": 1754065686000, "results": "19", "hashOfConfig": "14"}, {"size": 8933, "mtime": 1754065804000, "results": "20", "hashOfConfig": "14"}, {"size": 4682, "mtime": 1754065458000, "results": "21", "hashOfConfig": "14"}, {"size": 2913, "mtime": 1754076652247, "results": "22", "hashOfConfig": "14"}, {"size": 8624, "mtime": 1754065930000, "results": "23", "hashOfConfig": "14"}, {"size": 4876, "mtime": 1754067937000, "results": "24", "hashOfConfig": "14"}, {"size": 14200, "mtime": 1754065856000, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "o7ggj6", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\AppHybrid.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\YouTubeWebView.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\DeveloperInfo.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\SidebarEnhanced.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\utils\\adBlocker.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\utils\\developerMonetization.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\utils\\youtubeApi.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\Header.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\SettingsEnhanced.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\utils\\channelConfig.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\DeveloperPanel.js", [], []]