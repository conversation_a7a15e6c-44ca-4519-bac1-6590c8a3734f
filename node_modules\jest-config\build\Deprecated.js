'use strict';

Object.defineProperty(exports, '__esModule', {
  value: true
});
exports.default = void 0;

function _chalk() {
  const data = _interopRequireDefault(require('chalk'));

  _chalk = function () {
    return data;
  };

  return data;
}

function _prettyFormat() {
  const data = require('pretty-format');

  _prettyFormat = function () {
    return data;
  };

  return data;
}

function _interopRequireDefault(obj) {
  return obj && obj.__esModule ? obj : {default: obj};
}

/**
 * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
const format = value =>
  (0, _prettyFormat().format)(value, {
    min: true
  });

const deprecatedOptions = {
  browser: () =>
    `  Option ${_chalk().default.bold(
      '"browser"'
    )} has been deprecated. Please install "browser-resolve" and use the "resolver" option in Jest configuration as shown in the documentation: https://jestjs.io/docs/configuration#resolver-string`,
  preprocessorIgnorePatterns: options => `  Option ${_chalk().default.bold(
    '"preprocessorIgnorePatterns"'
  )} was replaced by ${_chalk().default.bold(
    '"transformIgnorePatterns"'
  )}, which support multiple preprocessors.

  Jest now treats your current configuration as:
  {
    ${_chalk().default.bold(
      '"transformIgnorePatterns"'
    )}: ${_chalk().default.bold(format(options.preprocessorIgnorePatterns))}
  }

  Please update your configuration.`,
  scriptPreprocessor: options => `  Option ${_chalk().default.bold(
    '"scriptPreprocessor"'
  )} was replaced by ${_chalk().default.bold(
    '"transform"'
  )}, which support multiple preprocessors.

  Jest now treats your current configuration as:
  {
    ${_chalk().default.bold('"transform"')}: ${_chalk().default.bold(
    `{".*": ${format(options.scriptPreprocessor)}}`
  )}
  }

  Please update your configuration.`,
  setupTestFrameworkScriptFile: _options => `  Option ${_chalk().default.bold(
    '"setupTestFrameworkScriptFile"'
  )} was replaced by configuration ${_chalk().default.bold(
    '"setupFilesAfterEnv"'
  )}, which supports multiple paths.

  Please update your configuration.`,
  testPathDirs: options => `  Option ${_chalk().default.bold(
    '"testPathDirs"'
  )} was replaced by ${_chalk().default.bold('"roots"')}.

  Jest now treats your current configuration as:
  {
    ${_chalk().default.bold('"roots"')}: ${_chalk().default.bold(
    format(options.testPathDirs)
  )}
  }

  Please update your configuration.
  `
};
var _default = deprecatedOptions;
exports.default = _default;
