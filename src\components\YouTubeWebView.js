import React, { useRef, useEffect, useState } from 'react';

const YouTubeWebView = ({ searchQuery, onVideoChange, settings }) => {
  const iframeRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentUrl, setCurrentUrl] = useState('https://www.youtube.com');
  const [canGoBack, setCanGoBack] = useState(false);
  const [canGoForward, setCanGoForward] = useState(false);
  const [history, setHistory] = useState(['https://www.youtube.com']);
  const [historyIndex, setHistoryIndex] = useState(0);

  useEffect(() => {
    const iframe = iframeRef.current;
    if (!iframe) return;

    // معالجة تحميل الصفحة
    const handleLoad = () => {
      setIsLoading(false);
      console.log('✅ تم تحميل يوتيوب بنجاح');

      // تحديث حالة التنقل
      updateNavigationState();

      // تطبيق مانع الإعلانات
      setTimeout(() => {
        injectAdBlockerScript();
        injectCustomStyles();
      }, 1000);
    };

    const handleLoadStart = () => {
      setIsLoading(true);
      console.log('🔄 جاري تحميل يوتيوب...');
    };

    // إضافة المستمعين
    iframe.addEventListener('load', handleLoad);
    iframe.addEventListener('loadstart', handleLoadStart);

    return () => {
      iframe.removeEventListener('load', handleLoad);
      iframe.removeEventListener('loadstart', handleLoadStart);
    };
  }, []);

  // تحديث حالة التنقل
  const updateNavigationState = () => {
    setCanGoBack(historyIndex > 0);
    setCanGoForward(historyIndex < history.length - 1);
  };

  // حقن سكريبت مانع الإعلانات
  const injectAdBlockerScript = () => {
    const iframe = iframeRef.current;
    if (!iframe || !iframe.contentWindow) return;

    try {

    const adBlockScript = `
      // مانع إعلانات YouTube متقدم
      (function() {
        console.log('🛡️ تم تفعيل مانع الإعلانات');
        
        // إخفاء الإعلانات الموجودة
        function hideAds() {
          const adSelectors = [
            '.ytd-display-ad-renderer',
            '.ytd-promoted-sparkles-web-renderer',
            '.ytd-ad-slot-renderer',
            '.ytp-ad-module',
            '.ytp-ad-overlay-container',
            '.ytp-ad-text-overlay',
            '.video-ads',
            '.masthead-ad-control',
            '#player-ads',
            '.ad-container',
            '.ytd-banner-promo-renderer',
            '.ytd-video-masthead-ad-v3-renderer',
            '.ytd-primetime-promo-renderer'
          ];
          
          adSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
              el.style.display = 'none';
              el.style.visibility = 'hidden';
              el.style.opacity = '0';
              el.style.height = '0';
              el.style.width = '0';
            });
          });
        }
        
        // تشغيل مانع الإعلانات كل ثانية
        setInterval(hideAds, 1000);
        
        // تشغيل فوري
        hideAds();
        
        // مراقبة تغييرات DOM
        const observer = new MutationObserver(hideAds);
        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
        
        // تخطي الإعلانات تلقائياً
        function skipAds() {
          const skipButton = document.querySelector('.ytp-ad-skip-button, .ytp-skip-ad-button');
          if (skipButton) {
            skipButton.click();
            console.log('⏭️ تم تخطي الإعلان تلقائياً');
          }
        }
        
        setInterval(skipAds, 500);
      })();
    `;

      iframe.contentWindow.eval(adBlockScript);
      console.log('🛡️ تم حقن مانع الإعلانات في iframe');
    } catch (error) {
      console.warn('⚠️ لا يمكن حقن مانع الإعلانات بسبب CORS:', error);
    }
  };

  // حقن الأنماط المخصصة
  const injectCustomStyles = () => {
    const iframe = iframeRef.current;
    if (!iframe || !iframe.contentDocument) return;

    try {

    const customCSS = `
      /* تحسين مظهر YouTube */
      #masthead {
        background: #1a1a1a !important;
      }
      
      /* إخفاء العناصر غير المرغوبة */
      .ytd-guide-renderer,
      #guide-button,
      #voice-search-button {
        display: none !important;
      }
      
      /* تحسين التخطيط */
      #primary {
        margin: 0 auto !important;
      }
      
      /* ثيم داكن محسن */
      body {
        background: #0f0f0f !important;
      }
    `;

      const style = iframe.contentDocument.createElement('style');
      style.textContent = customCSS;
      iframe.contentDocument.head.appendChild(style);
      console.log('🎨 تم حقن الأنماط المخصصة');
    } catch (error) {
      console.warn('⚠️ لا يمكن حقن الأنماط بسبب CORS:', error);
    }
  };

  // البحث في YouTube
  useEffect(() => {
    if (searchQuery && iframeRef.current) {
      const searchUrl = `https://www.youtube.com/results?search_query=${encodeURIComponent(searchQuery)}`;
      navigateToUrl(searchUrl);
    }
  }, [searchQuery]);

  // التنقل إلى رابط جديد
  const navigateToUrl = (url) => {
    if (iframeRef.current) {
      iframeRef.current.src = url;

      // تحديث التاريخ
      const newHistory = history.slice(0, historyIndex + 1);
      newHistory.push(url);
      setHistory(newHistory);
      setHistoryIndex(newHistory.length - 1);
      setCurrentUrl(url);

      console.log('🔗 التنقل إلى:', url);
    }
  };

  // التنقل
  const goBack = () => {
    if (canGoBack && historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setHistoryIndex(newIndex);
      const url = history[newIndex];
      iframeRef.current.src = url;
      setCurrentUrl(url);
      updateNavigationState();
    }
  };

  const goForward = () => {
    if (canGoForward && historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1;
      setHistoryIndex(newIndex);
      const url = history[newIndex];
      iframeRef.current.src = url;
      setCurrentUrl(url);
      updateNavigationState();
    }
  };

  const reload = () => {
    if (iframeRef.current) {
      const currentSrc = iframeRef.current.src;
      iframeRef.current.src = '';
      setTimeout(() => {
        iframeRef.current.src = currentSrc;
      }, 100);
      console.log('🔄 إعادة تحميل الصفحة');
    }
  };

  const goHome = () => {
    navigateToUrl('https://www.youtube.com');
  };

  return (
    <div className="youtube-webview-container">
      {/* شريط التنقل */}
      <div className="webview-navigation">
        <button 
          onClick={goBack} 
          disabled={!canGoBack}
          className="nav-btn"
          title="السابق"
        >
          ←
        </button>
        
        <button 
          onClick={goForward} 
          disabled={!canGoForward}
          className="nav-btn"
          title="التالي"
        >
          →
        </button>
        
        <button 
          onClick={reload}
          className="nav-btn"
          title="إعادة تحميل"
        >
          ↻
        </button>
        
        <button 
          onClick={goHome}
          className="nav-btn"
          title="الصفحة الرئيسية"
        >
          🏠
        </button>
        
        <div className="url-display">
          {currentUrl}
        </div>
        
        {isLoading && (
          <div className="loading-indicator">
            جاري التحميل...
          </div>
        )}
      </div>

      {/* YouTube iframe */}
      <iframe
        ref={iframeRef}
        src="https://www.youtube.com"
        className="youtube-webview"
        title="YouTube Player"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
      />
    </div>
  );
};

export default YouTubeWebView;
