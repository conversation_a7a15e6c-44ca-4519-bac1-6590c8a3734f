import React, { useRef, useEffect, useState } from 'react';
import { adBlocker } from '../utils/adBlocker';

const YouTubeWebView = ({ searchQuery, onVideoChange, settings }) => {
  const webviewRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentUrl, setCurrentUrl] = useState('https://www.youtube.com');
  const [canGoBack, setCanGoBack] = useState(false);
  const [canGoForward, setCanGoForward] = useState(false);

  useEffect(() => {
    const webview = webviewRef.current;
    if (!webview) return;

    // معالجة تحميل الصفحة
    const handleLoadStart = () => {
      setIsLoading(true);
    };

    const handleLoadStop = () => {
      setIsLoading(false);
      injectAdBlockerScript();
      injectCustomStyles();
      updateNavigationState();
    };

    // معالجة تغيير URL
    const handleNavigate = (e) => {
      setCurrentUrl(e.url);
      updateNavigationState();
    };

    // معالجة الأخطاء
    const handleError = (e) => {
      console.error('WebView Error:', e);
      setIsLoading(false);
    };

    // إضافة المستمعين
    webview.addEventListener('did-start-loading', handleLoadStart);
    webview.addEventListener('did-stop-loading', handleLoadStop);
    webview.addEventListener('did-navigate', handleNavigate);
    webview.addEventListener('did-navigate-in-page', handleNavigate);
    webview.addEventListener('did-fail-load', handleError);

    return () => {
      webview.removeEventListener('did-start-loading', handleLoadStart);
      webview.removeEventListener('did-stop-loading', handleLoadStop);
      webview.removeEventListener('did-navigate', handleNavigate);
      webview.removeEventListener('did-navigate-in-page', handleNavigate);
      webview.removeEventListener('did-fail-load', handleError);
    };
  }, []);

  // تحديث حالة التنقل
  const updateNavigationState = () => {
    const webview = webviewRef.current;
    if (webview) {
      setCanGoBack(webview.canGoBack());
      setCanGoForward(webview.canGoForward());
    }
  };

  // حقن سكريبت مانع الإعلانات
  const injectAdBlockerScript = () => {
    const webview = webviewRef.current;
    if (!webview) return;

    const adBlockScript = `
      // مانع إعلانات YouTube متقدم
      (function() {
        console.log('🛡️ تم تفعيل مانع الإعلانات');
        
        // إخفاء الإعلانات الموجودة
        function hideAds() {
          const adSelectors = [
            '.ytd-display-ad-renderer',
            '.ytd-promoted-sparkles-web-renderer',
            '.ytd-ad-slot-renderer',
            '.ytp-ad-module',
            '.ytp-ad-overlay-container',
            '.ytp-ad-text-overlay',
            '.video-ads',
            '.masthead-ad-control',
            '#player-ads',
            '.ad-container',
            '.ytd-banner-promo-renderer',
            '.ytd-video-masthead-ad-v3-renderer',
            '.ytd-primetime-promo-renderer'
          ];
          
          adSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
              el.style.display = 'none';
              el.style.visibility = 'hidden';
              el.style.opacity = '0';
              el.style.height = '0';
              el.style.width = '0';
            });
          });
        }
        
        // تشغيل مانع الإعلانات كل ثانية
        setInterval(hideAds, 1000);
        
        // تشغيل فوري
        hideAds();
        
        // مراقبة تغييرات DOM
        const observer = new MutationObserver(hideAds);
        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
        
        // تخطي الإعلانات تلقائياً
        function skipAds() {
          const skipButton = document.querySelector('.ytp-ad-skip-button, .ytp-skip-ad-button');
          if (skipButton) {
            skipButton.click();
            console.log('⏭️ تم تخطي الإعلان تلقائياً');
          }
        }
        
        setInterval(skipAds, 500);
      })();
    `;

    webview.executeJavaScript(adBlockScript);
  };

  // حقن الأنماط المخصصة
  const injectCustomStyles = () => {
    const webview = webviewRef.current;
    if (!webview) return;

    const customCSS = `
      /* تحسين مظهر YouTube */
      #masthead {
        background: #1a1a1a !important;
      }
      
      /* إخفاء العناصر غير المرغوبة */
      .ytd-guide-renderer,
      #guide-button,
      #voice-search-button {
        display: none !important;
      }
      
      /* تحسين التخطيط */
      #primary {
        margin: 0 auto !important;
      }
      
      /* ثيم داكن محسن */
      body {
        background: #0f0f0f !important;
      }
    `;

    webview.insertCSS(customCSS);
  };

  // البحث في YouTube
  useEffect(() => {
    if (searchQuery && webviewRef.current) {
      const searchUrl = `https://www.youtube.com/results?search_query=${encodeURIComponent(searchQuery)}`;
      webviewRef.current.loadURL(searchUrl);
    }
  }, [searchQuery]);

  // التنقل
  const goBack = () => {
    if (webviewRef.current && canGoBack) {
      webviewRef.current.goBack();
    }
  };

  const goForward = () => {
    if (webviewRef.current && canGoForward) {
      webviewRef.current.goForward();
    }
  };

  const reload = () => {
    if (webviewRef.current) {
      webviewRef.current.reload();
    }
  };

  const goHome = () => {
    if (webviewRef.current) {
      webviewRef.current.loadURL('https://www.youtube.com');
    }
  };

  return (
    <div className="youtube-webview-container">
      {/* شريط التنقل */}
      <div className="webview-navigation">
        <button 
          onClick={goBack} 
          disabled={!canGoBack}
          className="nav-btn"
          title="السابق"
        >
          ←
        </button>
        
        <button 
          onClick={goForward} 
          disabled={!canGoForward}
          className="nav-btn"
          title="التالي"
        >
          →
        </button>
        
        <button 
          onClick={reload}
          className="nav-btn"
          title="إعادة تحميل"
        >
          ↻
        </button>
        
        <button 
          onClick={goHome}
          className="nav-btn"
          title="الصفحة الرئيسية"
        >
          🏠
        </button>
        
        <div className="url-display">
          {currentUrl}
        </div>
        
        {isLoading && (
          <div className="loading-indicator">
            جاري التحميل...
          </div>
        )}
      </div>

      {/* WebView */}
      <webview
        ref={webviewRef}
        src="https://www.youtube.com"
        className="youtube-webview"
        allowpopups="true"
        webpreferences="allowRunningInsecureContent"
      />
    </div>
  );
};

export default YouTubeWebView;
