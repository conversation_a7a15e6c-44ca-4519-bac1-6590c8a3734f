const { app, BrowserWindow, Menu, ipc<PERSON>ain, session } = require('electron');
const path = require('path');
const isDev = process.env.ELECTRON_IS_DEV === '1';

let mainWindow;

// إعداد مانع الإعلانات المحسن
const adBlockFilters = [
  // إعلانات YouTube الأساسية
  '*://*.doubleclick.net/*',
  '*://*.googleadservices.com/*',
  '*://*.googlesyndication.com/*',
  '*://googleads.g.doubleclick.net/*',
  '*://*.youtube.com/api/stats/ads*',
  '*://*.youtube.com/ptracking*',
  '*://*.youtube.com/pagead/*',
  '*://www.youtube.com/api/stats/ads*',
  '*://www.youtube.com/ptracking*',
  '*://www.youtube.com/pagead/*',

  // إعلانات YouTube المتقدمة
  '*://*.youtube.com/get_video_info*ad*',
  '*://*.youtube.com/youtubei/v1/player/ad_break*',
  '*://*.youtube.com/api/stats/watchtime*',
  '*://www.youtube.com/api/stats/watchtime*',
  '*://*.youtube.com/youtubei/v1/log_event*',

  // إعلانات Google العامة
  '*://*.adsystem.com/*',
  '*://*.amazon-adsystem.com/*',
  '*://*.google-analytics.com/*',
  '*://*.googletagmanager.com/*',

  // متتبعات أخرى
  '*://*.scorecardresearch.com/*',
  '*://*.outbrain.com/*',
  '*://*.taboola.com/*',
  '*://*.adsafeprotected.com/*',
  '*://*.moatads.com/*'
];

// CSS لإخفاء الإعلانات
const adBlockCSS = `
  /* إخفاء إعلانات YouTube */
  .ytd-display-ad-renderer,
  .ytd-promoted-sparkles-web-renderer,
  .ytd-ad-slot-renderer,
  .ytp-ad-module,
  .ytp-ad-overlay-container,
  .ytp-ad-text-overlay,
  .video-ads,
  .masthead-ad-control,
  #player-ads,
  .ad-container,
  .ytd-banner-promo-renderer,
  .ytd-video-masthead-ad-v3-renderer,
  .ytd-primetime-promo-renderer {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    width: 0 !important;
  }

  /* تحسين مظهر الصفحة */
  #masthead {
    background: #1a1a1a !important;
  }

  /* إخفاء عناصر غير مرغوبة */
  .ytd-guide-renderer,
  #guide,
  #secondary {
    display: none !important;
  }

  /* توسيط المحتوى */
  #primary {
    margin: 0 auto !important;
    max-width: 100% !important;
  }
`;

function createWindow() {
  // إنشاء نافذة المتصفح
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1000,
    minHeight: 700,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
      webSecurity: false,
      webviewTag: true,
      allowRunningInsecureContent: true
    },
    icon: path.join(__dirname, '../assets/icon.png'),
    title: 'مشغل يوتيوب المتقدم',
    show: false,
    backgroundColor: '#1a1a1a',
    titleBarStyle: 'hidden',
    frame: false
  });

  // تحميل التطبيق الهجين الجديد
  const startUrl = isDev
    ? 'http://localhost:3000'  // في وضع التطوير نحمل التطبيق المحلي
    : `file://${path.join(__dirname, '../build/index.html')}`; // في الإنتاج نحمل الملفات المبنية

  mainWindow.loadURL(startUrl);

  // إظهار النافذة عند الانتهاء من التحميل
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();

    // حقن CSS لمانع الإعلانات بعد تحميل الصفحة
    setTimeout(() => {
      injectAdBlockCSS();
    }, 2000);
  });

  // إعداد مانع الإعلانات المحسن
  setupAdBlocker();

  // إعداد معالجات الرسائل
  setupIPCHandlers();

  // فتح أدوات المطور في وضع التطوير
  if (isDev) {
    mainWindow.webContents.openDevTools();
  }

  // إزالة شريط القوائم
  Menu.setApplicationMenu(null);



  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// إعداد مانع الإعلانات
function setupAdBlocker() {
  // حجب الطلبات الإعلانية
  session.defaultSession.webRequest.onBeforeRequest({ urls: adBlockFilters }, (details, callback) => {
    console.log('🛡️ تم حجب إعلان:', details.url);
    callback({ cancel: true });
  });

  // تعديل الهيدرز لمنع التتبع
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    const responseHeaders = { ...details.responseHeaders };

    // إزالة هيدرز التتبع
    delete responseHeaders['x-frame-options'];
    delete responseHeaders['content-security-policy'];

    callback({ responseHeaders });
  });

  // حجب الإعلانات بناءً على نوع المحتوى
  session.defaultSession.webRequest.onBeforeRequest({ urls: ['*://*/*'] }, (details, callback) => {
    const url = details.url.toLowerCase();

    // حجب الإعلانات المخفية
    if (url.includes('ads') ||
        url.includes('advertisement') ||
        url.includes('doubleclick') ||
        url.includes('googleadservices') ||
        url.includes('googlesyndication') ||
        url.includes('adsystem') ||
        (url.includes('youtube.com') && url.includes('ad'))) {
      console.log('🛡️ تم حجب محتوى إعلاني:', details.url);
      callback({ cancel: true });
      return;
    }

    callback({});
  });
}

// حقن CSS لمانع الإعلانات
function injectAdBlockCSS() {
  if (mainWindow && mainWindow.webContents) {
    mainWindow.webContents.insertCSS(adBlockCSS);
    console.log('✅ تم حقن CSS مانع الإعلانات');
  }
}

// إعداد معالجات IPC
function setupIPCHandlers() {
  // معالجة الرسائل من العملية المُرسِلة
  ipcMain.handle('get-app-version', () => {
    return app.getVersion();
  });

  ipcMain.handle('minimize-window', () => {
    if (mainWindow) {
      mainWindow.minimize();
    }
  });

  ipcMain.handle('maximize-window', () => {
    if (mainWindow) {
      if (mainWindow.isMaximized()) {
        mainWindow.unmaximize();
      } else {
        mainWindow.maximize();
      }
    }
  });

  ipcMain.handle('close-window', () => {
    if (mainWindow) {
      mainWindow.close();
    }
  });

  // معالجة حقن CSS إضافي
  ipcMain.handle('inject-css', (event, css) => {
    if (mainWindow && mainWindow.webContents) {
      mainWindow.webContents.insertCSS(css);
      return true;
    }
    return false;
  });

  // معالجة تنفيذ JavaScript
  ipcMain.handle('execute-js', (event, script) => {
    if (mainWindow && mainWindow.webContents) {
      return mainWindow.webContents.executeJavaScript(script);
    }
    return null;
  });
}

// هذه الطريقة ستُستدعى عندما يكون Electron جاهزاً
app.whenReady().then(createWindow);

// الخروج عندما تُغلق جميع النوافذ
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});



