{"name": "jest-runner", "version": "27.5.1", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/jest-runner"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/console": "^27.5.1", "@jest/environment": "^27.5.1", "@jest/test-result": "^27.5.1", "@jest/transform": "^27.5.1", "@jest/types": "^27.5.1", "@types/node": "*", "chalk": "^4.0.0", "emittery": "^0.8.1", "graceful-fs": "^4.2.9", "jest-docblock": "^27.5.1", "jest-environment-jsdom": "^27.5.1", "jest-environment-node": "^27.5.1", "jest-haste-map": "^27.5.1", "jest-leak-detector": "^27.5.1", "jest-message-util": "^27.5.1", "jest-resolve": "^27.5.1", "jest-runtime": "^27.5.1", "jest-util": "^27.5.1", "jest-worker": "^27.5.1", "source-map-support": "^0.5.6", "throat": "^6.0.1"}, "devDependencies": {"@types/exit": "^0.1.30", "@types/graceful-fs": "^4.1.2", "@types/source-map-support": "^0.5.0", "jest-jasmine2": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850"}