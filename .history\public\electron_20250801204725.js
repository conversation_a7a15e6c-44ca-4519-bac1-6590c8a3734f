const { app, BrowserWindow, Menu, ipcMain, session } = require('electron');
const path = require('path');
const isDev = process.env.ELECTRON_IS_DEV === '1';

let mainWindow;

// إعداد مانع الإعلانات
const adBlockFilters = [
  '*://*.doubleclick.net/*',
  '*://*.googleadservices.com/*',
  '*://*.googlesyndication.com/*',
  '*://googleads.g.doubleclick.net/*',
  '*://*.youtube.com/api/stats/ads*',
  '*://*.youtube.com/ptracking*',
  '*://*.youtube.com/pagead/*',
  '*://www.youtube.com/api/stats/ads*',
  '*://www.youtube.com/ptracking*',
  '*://www.youtube.com/pagead/*'
];

function createWindow() {
  // إنشاء نافذة المتصفح
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
      webSecurity: false
    },
    icon: path.join(__dirname, '../assets/icon.png'),
    title: 'مشغل يوتيوب',
    show: false,
    backgroundColor: '#1a1a1a'
  });

  // تحميل التطبيق - تم تعديله ليشغل موقع يوتيوب مباشرة
  const startUrl = isDev
    ? 'https://www.youtube.com'  // في وضع التطوير نحمل يوتيوب مباشرة
    : 'https://www.youtube.com'; // في الإنتاج أيضاً نحمل يوتيوب مباشرة

  mainWindow.loadURL(startUrl);

  // إظهار النافذة عند الانتهاء من التحميل
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // إعداد مانع الإعلانات
  session.defaultSession.webRequest.onBeforeRequest({ urls: adBlockFilters }, (details, callback) => {
    callback({ cancel: true });
  });

  // إضافة فلاتر إضافية لمنع الإعلانات
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': ['default-src \'self\' \'unsafe-inline\' \'unsafe-eval\' data: https://www.youtube.com https://youtube.com https://i.ytimg.com https://yt3.ggpht.com;']
      }
    });
  });

  // فتح أدوات المطور في وضع التطوير
  if (isDev) {
    mainWindow.webContents.openDevTools();
  }

  // إزالة شريط القوائم
  Menu.setApplicationMenu(null);

  // إضافة اختصارات لوحة المفاتيح
  mainWindow.webContents.on('before-input-event', (event, input) => {
    // F11 للشاشة الكاملة
    if (input.key === 'F11' && input.type === 'keyDown') {
      if (mainWindow.isFullScreen()) {
        mainWindow.setFullScreen(false);
      } else {
        mainWindow.setFullScreen(true);
      }
    }

    // Ctrl+R لإعادة التحميل
    if (input.control && input.key === 'r' && input.type === 'keyDown') {
      mainWindow.webContents.reload();
    }

    // Ctrl+Shift+I لأدوات المطور
    if (input.control && input.shift && input.key === 'I' && input.type === 'keyDown') {
      mainWindow.webContents.toggleDevTools();
    }
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// هذه الطريقة ستُستدعى عندما يكون Electron جاهزاً
app.whenReady().then(createWindow);

// الخروج عندما تُغلق جميع النوافذ
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// معالجة الرسائل من العملية المُرسِلة
ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

ipcMain.handle('minimize-window', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('maximize-window', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  }
});

ipcMain.handle('close-window', () => {
  if (mainWindow) {
    mainWindow.close();
  }
});

