const { app, BrowserWindow, Menu, ipcMain, session } = require('electron');
const path = require('path');
const isDev = process.env.ELECTRON_IS_DEV === '1';

let mainWindow;

// إعداد مانع الإعلانات
const adBlockFilters = [
  '*://*.doubleclick.net/*',
  '*://*.googleadservices.com/*',
  '*://*.googlesyndication.com/*',
  '*://googleads.g.doubleclick.net/*',
  '*://*.youtube.com/api/stats/ads*',
  '*://*.youtube.com/ptracking*',
  '*://*.youtube.com/pagead/*',
  '*://www.youtube.com/api/stats/ads*',
  '*://www.youtube.com/ptracking*',
  '*://www.youtube.com/pagead/*'
];

function createWindow() {
  // إنشاء نافذة المتصفح
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
      webSecurity: false
    },
    icon: path.join(__dirname, '../assets/icon.png'),
    title: 'مشغل يوتيوب',
    show: false,
    backgroundColor: '#1a1a1a'
  });

  // تحميل التطبيق - تم تعديله ليشغل موقع يوتيوب مباشرة
  const startUrl = isDev
    ? 'https://www.youtube.com'  // في وضع التطوير نحمل يوتيوب مباشرة
    : 'https://www.youtube.com'; // في الإنتاج أيضاً نحمل يوتيوب مباشرة

  mainWindow.loadURL(startUrl);

  // إظهار النافذة عند الانتهاء من التحميل
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // إعداد مانع الإعلانات المحسن
  session.defaultSession.webRequest.onBeforeRequest({ urls: adBlockFilters }, (details, callback) => {
    console.log('🚫 تم حجب إعلان:', details.url);
    callback({ cancel: true });
  });

  // حجب الإعلانات بناءً على نوع المحتوى
  session.defaultSession.webRequest.onBeforeRequest({ urls: ['*://*/*'] }, (details, callback) => {
    const url = details.url.toLowerCase();

    // حجب الإعلانات المخفية
    if (url.includes('ads') ||
        url.includes('advertisement') ||
        url.includes('doubleclick') ||
        url.includes('googleadservices') ||
        url.includes('googlesyndication') ||
        url.includes('adsystem') ||
        (url.includes('youtube.com') && url.includes('ad'))) {
      console.log('🛡️ تم حجب محتوى إعلاني:', details.url);
      callback({ cancel: true });
      return;
    }

    callback({});
  });

  // تحسين رؤوس HTTP
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    const responseHeaders = { ...details.responseHeaders };

    // إزالة رؤوس التتبع
    delete responseHeaders['x-client-data'];
    delete responseHeaders['x-goog-api-key'];

    // تحسين الأمان
    responseHeaders['X-Frame-Options'] = ['DENY'];
    responseHeaders['X-Content-Type-Options'] = ['nosniff'];

    callback({ responseHeaders });
  });

  // حجب ملفات تعريف الارتباط الإعلانية
  session.defaultSession.webRequest.onBeforeSendHeaders({ urls: ['*://*/*'] }, (details, callback) => {
    const headers = { ...details.requestHeaders };

    // إزالة رؤوس التتبع
    delete headers['X-Client-Data'];
    delete headers['X-Goog-Api-Key'];

    callback({ requestHeaders: headers });
  });

  // إضافة تحسينات إضافية للواجهة
  mainWindow.webContents.on('did-finish-load', () => {
    // حقن CSS إضافي لتحسين التجربة
    mainWindow.webContents.insertCSS(`
      /* إخفاء العناصر المشتتة الإضافية */
      ytd-consent-bump-v2-lightbox,
      ytd-single-column-browse-results-renderer > #primary > #contents > ytd-rich-section-renderer:has(ytd-rich-shelf-renderer[is-shorts]),
      #shorts-shelf,
      ytd-reel-shelf-renderer,
      .ytd-rich-shelf-renderer[is-shorts] {
        display: none !important;
      }

      /* تحسين مظهر الصفحة الرئيسية */
      #masthead {
        background-color: #0f0f0f !important;
      }

      /* تحسين مشغل الفيديو */
      .html5-video-player .ytp-chrome-bottom {
        background: linear-gradient(transparent, rgba(0,0,0,0.8)) !important;
      }

      /* إزالة الحدود المشتتة */
      .ytp-gradient-top,
      .ytp-gradient-bottom {
        opacity: 0 !important;
      }

      /* تحسين الشريط الجانبي */
      #secondary-inner {
        background-color: #0f0f0f !important;
      }

      /* إخفاء الإشعارات المزعجة */
      .yt-alert-with-actions-renderer,
      .ytd-mealbar-promo-renderer,
      ytd-statement-banner-renderer {
        display: none !important;
      }
    `);

    console.log('🎨 تم تطبيق تحسينات الواجهة');
  });

  // فتح أدوات المطور في وضع التطوير
  if (isDev) {
    mainWindow.webContents.openDevTools();
  }

  // إزالة شريط القوائم
  Menu.setApplicationMenu(null);

  // إضافة اختصارات لوحة المفاتيح
  mainWindow.webContents.on('before-input-event', (event, input) => {
    // F11 للشاشة الكاملة
    if (input.key === 'F11' && input.type === 'keyDown') {
      if (mainWindow.isFullScreen()) {
        mainWindow.setFullScreen(false);
      } else {
        mainWindow.setFullScreen(true);
      }
    }

    // Ctrl+R لإعادة التحميل
    if (input.control && input.key === 'r' && input.type === 'keyDown') {
      mainWindow.webContents.reload();
    }

    // Ctrl+Shift+I لأدوات المطور
    if (input.control && input.shift && input.key === 'I' && input.type === 'keyDown') {
      mainWindow.webContents.toggleDevTools();
    }
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// هذه الطريقة ستُستدعى عندما يكون Electron جاهزاً
app.whenReady().then(createWindow);

// الخروج عندما تُغلق جميع النوافذ
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// معالجة الرسائل من العملية المُرسِلة
ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

ipcMain.handle('minimize-window', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('maximize-window', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  }
});

ipcMain.handle('close-window', () => {
  if (mainWindow) {
    mainWindow.close();
  }
});

